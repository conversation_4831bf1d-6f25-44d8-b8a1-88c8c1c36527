# 创建日志记录器
import logging
import time

from fastapi import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3 import Retry

from requests_custom import build_session_with_retries

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

client_key="CAP-1BF8A60111531B16D67EC73E7D6EAF33901B165DE5AA412E2CA2C77CC84B1684"

session = build_session_with_retries()

def image2text(image: str):
    request_body={
        "clientKey": client_key,
        "task": {
            "type": "ImageToTextTask",
            "module": "module_025",
            "body": image
        }
    }

    task_id: str
    resp=session.post("https://api.capsolver.com/createTask", json=request_body)
    if resp.status_code == 200:
        data = resp.json()

        error_id=data.get("errorId")

        if error_id==0:
            logger.info("打码任务提交成功")
            task_id= data.get("taskId")
            return data.get("solution").get("answers")[0]
        else:
            logger.error("打码任务提交失败*")
            return None
    else:
        logger.error(f"打码任务提交失败**")
        return None

    # for i in range(20):
    #     resp=session.post("https://api.capsolver.com/getTaskResult", json={"clientKey": client_key, "taskId": task_id})
    #     if resp.status_code == 200:
    #         data = resp.json()
    #         if data.get("status") == "ready":
    #             logger.info("打码任务完成")
    #             return data.get("solution").get("text")
    #         else:
    #             logger.error(f"打码任务未完成-第{i}次尝试*")
    #     else:
    #         logger.error(f"打码任务未完成-第{i}次尝试**")
    #     time.sleep(3)
    # return None