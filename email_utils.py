import logging

import time
import re

from requests.adapters import HTTPAdapter
from urllib3 import Retry

from requests_custom import build_session_with_retries

# 创建日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

mail_suffix="openai.edu.kg"

# API 基础 URL（根据您的邮局实际情况调整）
API_BASE_URL = "https://openai-edu-kg-email-worker.1198722360.workers.dev/api"


session = build_session_with_retries()


def create_email_and_get_jwt(domain=mail_suffix, cf_token=""):
    try:
        # 调用 API 创建新邮箱
        resp = session.post(
            f"{API_BASE_URL}/new_address",
            json={"name": "", "domain": domain, "cf_token": cf_token},
            timeout=15
        )

        # 检查响应状态
        if resp.status_code == 200:
            data = resp.json()
            address = data.get('address')  # 获取邮箱地址
            jwt = data.get('jwt')  # 获取 JWT token
            if address and jwt:
                print(f"[{address}] 创建成功")
                return address, jwt
            else:
                print("API 返回数据不完整")
                return None, None
        else:
            print(f"创建邮箱失败: {resp.status_code} - {resp.text}")
            return None, None
    except Exception as e:
        print(f"创建邮箱时发生异常: {e}")
        return None, None


def get_mails(jwt, limit=10, offset=0):
    """通过 API 获取邮件列表"""
    try:
        resp = session.get(
            f"{API_BASE_URL}/mails",
            headers={'Authorization': f'Bearer {jwt}'},
            params={'limit': limit, 'offset': offset},
            timeout=15
        )
        if resp.status_code == 200:
            return resp.json().get('results', [])
        else:
            print(f"获取邮件失败: {resp.status_code} - {resp.text}")
            return []
    except Exception as e:
        print(f"获取邮件时发生异常: {e}")
        return []


def get_verification_code(jwt, max_attempts: int = 12, interval: int = 5):
    """轮询邮箱, 提取 6 位数字验证码"""
    # 支持多种验证码格式的正则表达式
    patterns = re.compile(r"You=?\r?\nr code is[:：]?\s*([0-9]{6})", re.IGNORECASE)  # Genspark格式(考虑换行): "You=\r\nr code is: 046794"


    for attempt in range(1, max_attempts + 1):
        logger.info(f"第 {attempt}/{max_attempts} 次尝试获取验证码")
        try:
            mails = get_mails(jwt, limit=10, offset=0)
        except Exception as e:
            logger.error(f"获取邮件列表异常: {e}")
            mails = []

        for mail in reversed(mails):  # 从最新邮件开始检索
            raw = mail.get("raw", "")
            if not raw:
                continue

            # 尝试所有正则表达式模式
            for pattern in patterns:
                match = pattern.search(raw)
                if match:
                    code = match.group(1)
                    logger.info(f"成功提取验证码: {code}")
                    return code

        logger.info(f"{interval} 秒后重试…")
        time.sleep(interval)

    logger.error("验证码获取失败, 已达到最大重试次数")
    return None
