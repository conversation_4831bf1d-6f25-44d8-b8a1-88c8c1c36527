import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3 import Re<PERSON>


def build_session_with_retries(total_retries=5, backoff_factor=0.5, status_forcelist=(500, 502, 503, 504), allowed_methods=frozenset(['GET', 'POST'])):
    session = requests.Session()
    retry = Retry(total=total_retries, read=total_retries, connect=total_retries, backoff_factor=backoff_factor, status_forcelist=status_forcelist, allowed_methods=allowed_methods, raise_on_status=False)
    adapter = HTTPAdapter(max_retries=retry)
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    return session